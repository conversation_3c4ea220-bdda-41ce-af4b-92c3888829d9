import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/widget/livestream_audience_top_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/battle_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/live_stream_bottom_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/live_video_player.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/livestream_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/battle_start_countdown_overlay.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/live_stream_background_blur_image.dart';
import 'package:ratulive/utilities/theme_res.dart';
import 'package:ratulive/common/manager/logger.dart';

class LiveStreamAudienceScreen extends StatefulWidget {
  final Livestream livestream;
  final bool isHost;
  final bool isActive;

  const LiveStreamAudienceScreen({
    super.key,
    required this.livestream,
    required this.isHost,
    this.isActive = true,
  });

  @override
  State<LiveStreamAudienceScreen> createState() => _LiveStreamAudienceScreenState();
}

class _LiveStreamAudienceScreenState extends State<LiveStreamAudienceScreen> {
  Timer? _controllerCheckTimer;
  bool _hasRoomLoginFailed = false;
  String? _lastErrorMessage;

  @override
  void initState() {
    super.initState();
    Loggers.info('🚀 LiveStreamAudienceScreen initState - RoomID: ${widget.livestream.roomID}, IsActive: ${widget.isActive}');
    // If this is an active stream, ensure controller exists
    if (widget.isActive && widget.livestream.roomID != null) {
      _ensureControllerExists();
      _startControllerCheck();
    }
  }

  @override
  void didUpdateWidget(LiveStreamAudienceScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    Loggers.info('🔄 LiveStreamAudienceScreen didUpdateWidget - Old Active: ${oldWidget.isActive}, New Active: ${widget.isActive}');

    // If the active state changed to true, ensure controller exists
    if (!oldWidget.isActive && widget.isActive && widget.livestream.roomID != null) {
      Loggers.info('🚀 Stream became active, ensuring controller exists');
      _ensureControllerExists();
      _startControllerCheck();
    }

    // If the active state changed to false, stop checking
    if (oldWidget.isActive && !widget.isActive) {
      Loggers.info('🛑 Stream became inactive, stopping controller check');
      _controllerCheckTimer?.cancel();
    }
  }

  @override
  void dispose() {
    _controllerCheckTimer?.cancel();
    super.dispose();
  }

  // Ensure controller exists - create it if needed
  void _ensureControllerExists() {
    if (widget.livestream.roomID == null) return;

    try {
      // Try to find existing controller
      final controller = Get.find<LivestreamScreenController>(tag: widget.livestream.roomID);
      Loggers.success('✅ Controller found in _ensureControllerExists for room: ${widget.livestream.roomID}');

      // Check if controller has room login errors
      _checkControllerRoomState(controller);
    } catch (e) {
      // Controller doesn't exist and this is an active stream - create it
      if (widget.isActive) {
        try {
          Loggers.info('🔨 Creating controller in LiveStreamAudienceScreen for room: ${widget.livestream.roomID}');
          final controller = Get.put(
            LivestreamScreenController(widget.livestream.obs, widget.isHost),
            tag: widget.livestream.roomID,
          );
          Loggers.success('✅ Controller created in LiveStreamAudienceScreen for room: ${widget.livestream.roomID}');

          // Monitor the new controller for room state
          _checkControllerRoomState(controller);
        } catch (createError) {
          Loggers.error('❌ Failed to create controller in LiveStreamAudienceScreen: $createError');
          _hasRoomLoginFailed = true;
          _lastErrorMessage = 'Failed to create stream controller';
        }
      }
    }
  }

  void _checkControllerRoomState(LivestreamScreenController controller) {
    // This is a simplified check - in a real implementation, you might want to
    // listen to the controller's room state or add a room state observable
    // For now, we'll rely on the error messages shown by the controller
  }

  String _getStatusMessage(bool isActive) {
    if (_hasRoomLoginFailed) {
      return _lastErrorMessage ?? 'Connection Failed';
    }

    if (isActive) {
      // OPTIMIZATION: Show minimal loading states for instant switching
      try {
        final controller = Get.find<LivestreamScreenController>(tag: widget.livestream.roomID);
        if (controller.streamViews.isNotEmpty) {
          return 'Stream Connected';
        } else {
          // OPTIMIZATION: Show brief connecting message instead of long loading
          return 'Connecting...';
        }
      } catch (e) {
        // OPTIMIZATION: Controller should exist from pre-loading
        return 'Connecting...';
      }
    } else {
      return 'Stream Inactive';
    }
  }

  void _startControllerCheck() {
    _controllerCheckTimer?.cancel();
    int attempts = 0;
    const maxAttempts = 50; // 5 seconds max (100ms * 50)

    _controllerCheckTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      attempts++;

      if (widget.livestream.roomID != null) {
        try {
          Get.find<LivestreamScreenController>(tag: widget.livestream.roomID);
          // Controller found, stop checking and rebuild
          timer.cancel();
          if (mounted) {
            Loggers.success('🔄 Controller found in timer check after $attempts attempts, rebuilding UI');
            setState(() {});
          }
        } catch (e) {
          // Controller not found yet
          if (widget.isActive) {
            _ensureControllerExists();
          }

          // Stop trying after max attempts
          if (attempts >= maxAttempts) {
            timer.cancel();
            Loggers.error('❌ Controller check timed out after $maxAttempts attempts for room: ${widget.livestream.roomID}');
            if (mounted) {
              setState(() {}); // Force rebuild to show retry button
            }
          }
        }
      } else {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Try to get the controller - handle both managed and direct usage scenarios
    LivestreamScreenController? controller;

    Loggers.info('=== LiveStreamAudienceScreen Build ===');
    Loggers.info('RoomID: ${widget.livestream.roomID}');
    Loggers.info('IsActive: ${widget.isActive}');
    Loggers.info('IsHost: ${widget.isHost}');

    if (widget.livestream.roomID != null) {
      try {
        // First try to find existing controller (for managed scenarios)
        controller = Get.find<LivestreamScreenController>(tag: widget.livestream.roomID);
        Loggers.success('✅ Found existing controller for room: ${widget.livestream.roomID}, isActive: ${widget.isActive}');
      } catch (e) {
        // Controller not found
        Loggers.error('❌ Controller not found for room: ${widget.livestream.roomID}, isActive: ${widget.isActive}, error: $e');

        if (widget.isActive) {
          // For active streams, we expect the parent to have created the controller
          // If not found, it might be a timing issue - wait briefly and try again
          Loggers.warning('⚠️ Active stream but no controller found - this indicates a timing or creation issue');
          controller = null;
        } else {
          // Inactive stream, no controller needed
          Loggers.info('ℹ️ Stream inactive, no controller needed for room: ${widget.livestream.roomID}');
          controller = null;
        }
      }
    } else {
      Loggers.error('❌ RoomID is null for livestream');
    }

    Loggers.info('Final controller state: ${controller != null ? "FOUND" : "NULL"}');
    Loggers.info('Will show: ${controller == null ? (widget.isActive ? "Loading Stream..." : "Stream Inactive") : "Live Stream"}');
    Loggers.info('=== End Build Debug ===');

    return Scaffold(
      backgroundColor: blackPure(context),
      resizeToAvoidBottomInset: false,
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
          if (controller != null) {
            controller.onCloseAudienceBtn();
          }
        },
        child: controller == null
          ? _buildInactiveStreamView(widget.isActive)
          : Stack(
              children: [
                const LiveStreamBlurBackgroundImage(),

                /// Live StreamView
                Obx(() {
                  switch (controller!.liveData.value.type) {
                    case null:
                    case LivestreamType.livestream:
                      return LivestreamView(
                        streamViews: controller.streamViews,
                        controller: controller,
                      );
                    case LivestreamType.battle:
                      return BattleView(
                        isAudience: true,
                        controller: controller,
                        margin: const EdgeInsets.only(top: 100),
                      );
                    case LivestreamType.dummy:
                      return LivestreamVideoPlayer(
                          controller: controller.videoPlayerController);
                  }
                }),

                KeyboardAvoider(
                  autoScroll: false, // Prevent automatic scrolling that might cause glitches
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      LiveStreamAudienceTopView(
                          isAudience: true, controller: controller),
                      LiveStreamBottomView(
                          isAudience: true, controller: controller),
                    ],
                  ),
                ),

                Obx(
                  () {
                    Livestream stream = controller!.liveData.value;
                    bool isBattle = stream.battleType == BattleType.waiting;
                    if (isBattle) {
                      return BattleStartCountdownOverlay(
                          isHost: widget.isHost, stream: stream);
                    }
                    return const SizedBox();
                  },
                )
              ],
            ),
      ),
    );
  }

  Widget _buildInactiveStreamView(bool isActive) {
    // Log what's being shown
    Loggers.info('🖼️ Building ${isActive ? "Loading" : "Inactive"} stream view for room: ${widget.livestream.roomID}');

    // If this is supposed to be active, try to create the controller one more time
    if (isActive && widget.livestream.roomID != null) {
      _ensureControllerExists();
    }

    return Stack(
      children: [
        const LiveStreamBlurBackgroundImage(),
        Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isActive ? Icons.live_tv : Icons.tv_off,
                  color: Colors.white,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  _getStatusMessage(isActive),
                  style: TextStyle(
                    color: whitePure(context),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.livestream.hostUser?.username ?? 'Unknown Host',
                  style: TextStyle(
                    color: whitePure(context).withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
                if (isActive) ...[
                  const SizedBox(height: 16),
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Force rebuild and controller creation
                      Loggers.info('🔄 Retry button pressed, forcing controller creation');
                      _ensureControllerExists();
                      if (mounted) setState(() {});
                    },
                    child: const Text('Retry Connection'),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }


}
