import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/common/widget/black_gradient_shadow.dart';
import 'package:ratulive/languages/languages_keys.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/model/livestream/livestream_user_state.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/host/widget/live_stream_host_top_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/view/livestream_comment_view.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/live_stream_like_button.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/live_stream_text_field.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/widget/livestream_exist_message_bar.dart';
import 'package:ratulive/utilities/asset_res.dart';
import 'package:ratulive/utilities/color_res.dart';

class LiveStreamBottomView extends StatefulWidget {
  final bool isAudience;
  final LivestreamScreenController controller;

  const LiveStreamBottomView(
      {super.key, this.isAudience = false, required this.controller});

  @override
  State<LiveStreamBottomView> createState() => _LiveStreamBottomViewState();
}

class _LiveStreamBottomViewState extends State<LiveStreamBottomView>
    with SingleTickerProviderStateMixin {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize animations properly to prevent first-scroll glitches
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: SizedBox(
        height: Get.height / 2.7,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            const BlackGradientShadow(height: 200),
            SafeArea(
              top: false,
              child: Column(
                spacing: 5,
                children: [
                  Expanded(
                    child: Obx(() {
                      bool isVisible = widget.controller.isViewVisible.value;
                      Livestream stream = widget.controller.liveData.value;

                      // Prevent animation glitches on first render
                      Duration animationDuration = _isInitialized
                          ? const Duration(milliseconds: 200)
                          : Duration.zero;
                      double animationOpacity = _isInitialized
                          ? (isVisible ? 1 : 0)
                          : 1.0; // Start fully visible to prevent flash

                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          children: [
                            Expanded(
                                child: AnimatedOpacity(
                                    duration: animationDuration,
                                    opacity: animationOpacity,
                                    child: LiveStreamCommentView(
                                        controller: widget.controller))),
                            Row(spacing: 5, children: [
                              if (stream.type != LivestreamType.battle)
                                AnimatedRotation(
                                  duration: animationDuration,
                                  turns: _isInitialized ? (isVisible ? 0 : 0.5) : 0,
                                  child: LiveStreamCircleBorderButton(
                                      image: AssetRes.icDownArrow_1,
                                      size: const Size(30, 30),
                                      onTap: widget.controller.toggleView),
                                ),
                              Expanded(
                                  child: AnimatedOpacity(
                                duration: animationDuration,
                                opacity: animationOpacity,
                                child: IgnorePointer(
                                  ignoring: !isVisible,
                                  child: LiveStreamTextFieldView(
                                      isAudience: widget.isAudience,
                                      controller: widget.controller),
                                ),
                              )),
                              AnimatedOpacity(
                                duration: animationDuration,
                                opacity: animationOpacity,
                                child: IgnorePointer(
                                  ignoring: !isVisible,
                                  child: LiveStreamLikeButton(
                                      onLikeTap: (p0) {
                                        widget.controller.onLikeTap = p0;
                                      },
                                      onTap: widget.controller.onLikeButtonTap),
                                ),
                              )
                            ]),
                            Obx(
                              () {
                                int? userId = controller.myUser.value?.id;
                                LivestreamUserState? userState =
                                    controller.liveUsersStates.firstWhereOrNull(
                                        (element) => element.userId == userId);
                                if (userState == null) return const SizedBox();
                                final isHostOrCoHost = userState.type ==
                                        LivestreamUserType.host ||
                                    userState.type == LivestreamUserType.coHost;
                                if (!isHostOrCoHost) return const SizedBox();
                                Livestream stream = controller.liveData.value;
                                bool isBattleRunning =
                                    stream.battleType == BattleType.running;

                                return Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  spacing: 10,
                                  children: [
                                    if (LivestreamUserType.coHost ==
                                            userState.type &&
                                        stream.type ==
                                            LivestreamType.livestream)
                                      LiveStreamCircleBorderButton(
                                          onTap: () {
                                            if (isBattleRunning) {
                                              controller.showSnackBar(LKey
                                                  .cannotLeaveDuringBattle.tr);
                                            } else {
                                              controller
                                                  .closeCoHostStream(userId);
                                            }
                                          },
                                          image: AssetRes.icClose,
                                          iconColor: ColorRes.likeRed,
                                          bgColor: ColorRes.likeRed,
                                          borderColor: ColorRes.likeRed
                                              .withValues(alpha: .2)),
                                    LiveStreamCircleBorderButton(
                                        image: AssetRes.icFlip,
                                        onTap: controller.toggleFlipCamera),
                                    LiveStreamCircleBorderButton(
                                        image: userState.audioStatus ==
                                                VideoAudioStatus.on
                                            ? AssetRes.icMicrophone
                                            : AssetRes.icMicOff,
                                        onTap: () =>
                                            controller.toggleMic(userState)),
                                    LiveStreamCircleBorderButton(
                                        image: userState.videoStatus ==
                                                VideoAudioStatus.on
                                            ? AssetRes.icVideoCamera
                                            : AssetRes.icVideoOff,
                                        onTap: () =>
                                            controller.toggleVideo(userState)),
                                  ],
                                );
                              },
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                  Obx(() {
                    Livestream stream = controller.liveData.value;
                    if ((stream.type == LivestreamType.battle &&
                            stream.battleType == BattleType.end) ||
                        controller.isMinViewerTimeout.value) {
                      return LivestreamExistMessageBar(
                          controller: controller, stream: stream);
                    } else {
                      return const SizedBox();
                    }
                  }),
                  // const SizedBox(height: 10),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
