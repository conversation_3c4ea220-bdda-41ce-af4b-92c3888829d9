import 'package:flutter/material.dart';

/// Custom scroll physics that prevents first-scroll glitches and provides smooth scrolling
class SmoothScrollPhysics extends ScrollPhysics {
  const SmoothScrollPhysics({super.parent});

  @override
  SmoothScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return SmoothScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  SpringDescription get spring => const SpringDescription(
        mass: 0.8,
        stiffness: 400,
        damping: 40,
      );

  @override
  double get minFlingVelocity => 50.0;

  @override
  double get maxFlingVelocity => 8000.0;

  @override
  Tolerance get tolerance => const Tolerance(
        velocity: 1.0,
        distance: 0.5,
      );

  @override
  double carriedMomentum(double existingVelocity) {
    // Reduce carried momentum to prevent jarring first-scroll behavior
    return existingVelocity * 0.8;
  }

  @override
  double frictionFactor(double overscrollFraction) {
    // Provide consistent friction to prevent glitches
    return 0.52 * overscrollFraction.clamp(0.0, 1.0);
  }
}

/// Scroll physics specifically designed for live stream comment views
class LiveStreamCommentScrollPhysics extends ScrollPhysics {
  const LiveStreamCommentScrollPhysics({super.parent});

  @override
  LiveStreamCommentScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return LiveStreamCommentScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  SpringDescription get spring => const SpringDescription(
        mass: 1.0,
        stiffness: 300,
        damping: 30,
      );

  @override
  double get minFlingVelocity => 100.0;

  @override
  double get maxFlingVelocity => 5000.0;

  @override
  Tolerance get tolerance => const Tolerance(
        velocity: 0.5,
        distance: 0.25,
      );

  @override
  double carriedMomentum(double existingVelocity) {
    // Minimal carried momentum for smooth comment scrolling
    return existingVelocity * 0.6;
  }

  @override
  double frictionFactor(double overscrollFraction) {
    // Higher friction for comment lists to prevent over-scrolling
    return 0.7 * overscrollFraction.clamp(0.0, 1.0);
  }

  @override
  bool shouldAcceptUserOffset(ScrollMetrics position) {
    // Always accept user offset to prevent first-scroll issues
    return true;
  }
}
