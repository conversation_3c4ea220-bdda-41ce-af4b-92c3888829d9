import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/room_manager.dart';
import 'package:ratulive/common/manager/logger.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  String? _currentActiveRoomID;

  // Track all created controllers for better management
  final Set<String> _createdControllers = <String>{};

  // Use global room manager
  final RoomManager _roomManager = RoomManager();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // OPTIMIZATION: Aggressive pre-loading for instant switching
    _initializeInstantSwitching();
  }

  void _initializeInstantSwitching() {
    // Create controllers for current and adjacent streams immediately
    _createControllerForIndex(_currentIndex, isActive: true);

    // Pre-load more streams for instant switching
    _preLoadMultipleStreams();

    // Set current active room
    if (_currentIndex >= 0 && _currentIndex < widget.livestreams.length) {
      _currentActiveRoomID = widget.livestreams[_currentIndex].roomID;
    }
  }

  @override
  void dispose() {
    // Clean up all created controllers
    _cleanupAllControllers();
    _pageController.dispose();
    super.dispose();
  }

  void _cleanupAllControllers() {
    Loggers.info('🧹 Cleaning up all controllers: ${_createdControllers.length} controllers');

    for (final roomID in _createdControllers.toList()) {
      try {
        final controller = Get.find<LivestreamScreenController>(tag: roomID);
        controller.logoutRoom(5).catchError((error) {
          Loggers.error('Error during logout for room $roomID: $error');
        });
        Get.delete<LivestreamScreenController>(tag: roomID);
        Loggers.info('✅ Controller cleaned up for room: $roomID');
      } catch (e) {
        Loggers.warning('⚠️ Controller not found during cleanup for room: $roomID');
      }
    }

    _createdControllers.clear();
    _currentActiveRoomID = null;
  }

  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      Loggers.info('⚡ INSTANT switch: $_currentIndex → $index');

      final oldIndex = _currentIndex;
      _currentIndex = index;

      // OPTIMIZATION: Instant activation - controller should already exist
      _instantActivateStream(index);

      // Deactivate previous stream (non-blocking)
      _deactivateStreamAtIndex(oldIndex);

      // Pre-load next streams for future instant switching
      _preLoadAdjacentStreams(index);

      // Force immediate UI rebuild
      if (mounted) {
        setState(() {});
      }

      Loggers.info('⚡ Instant switch completed: $oldIndex → $index');
    }
  }

  void _instantActivateStream(int index) {
    if (index >= 0 && index < widget.livestreams.length) {
      final roomID = widget.livestreams[index].roomID;
      if (roomID != null) {
        _currentActiveRoomID = roomID;

        // Controller should already exist from pre-loading
        try {
          Get.find<LivestreamScreenController>(tag: roomID);
          Loggers.info('⚡ Instant activation successful - controller ready: $roomID');
        } catch (e) {
          // Fallback: create controller immediately if not found
          Loggers.warning('⚠️ Controller not pre-loaded, creating instantly: $roomID');
          _createControllerForIndex(index, isActive: true);
        }
      }
    }
  }

  void _preLoadAdjacentStreams(int currentIndex) {
    // Pre-load streams around current position for next instant switch
    final prevIndex = currentIndex - 1;
    final nextIndex = currentIndex + 1;

    if (prevIndex >= 0) {
      _createControllerForIndex(prevIndex, isActive: false);
    }

    if (nextIndex < widget.livestreams.length) {
      _createControllerForIndex(nextIndex, isActive: false);
    }
  }

  void _preLoadMultipleStreams() {
    // OPTIMIZATION: Pre-load multiple streams for instant switching
    Loggers.info('🚀 Pre-loading streams for instant switching...');

    // Pre-load current +/- 2 streams for instant access
    final startIndex = (_currentIndex - 2).clamp(0, widget.livestreams.length - 1);
    final endIndex = (_currentIndex + 2).clamp(0, widget.livestreams.length - 1);

    for (int i = startIndex; i <= endIndex; i++) {
      if (i != _currentIndex) {
        // Pre-load but don't activate
        _createControllerForIndex(i, isActive: false);
      }
    }

    Loggers.info('✅ Pre-loaded streams from index $startIndex to $endIndex');
  }

  void _preCreateAdjacentControllers() {
    // Pre-create controllers for previous and next streams for smoother transitions
    final prevIndex = _currentIndex - 1;
    final nextIndex = _currentIndex + 1;

    if (prevIndex >= 0) {
      _createControllerForIndex(prevIndex, isActive: false);
    }

    if (nextIndex < widget.livestreams.length) {
      _createControllerForIndex(nextIndex, isActive: false);
    }
  }

  void _createControllerForIndex(int index, {bool isActive = true}) {
    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      if (roomID != null) {
        // OPTIMIZATION: Fast path for existing controllers
        if (_createdControllers.contains(roomID)) {
          try {
            Get.find<LivestreamScreenController>(tag: roomID);
            if (isActive) {
              Loggers.info('⚡ Fast activation - Controller exists for room: $roomID');
            }
            return;
          } catch (e) {
            _createdControllers.remove(roomID);
            _roomManager.cleanupRoom(roomID);
          }
        }

        // OPTIMIZATION: Skip room manager checks for faster creation
        try {
          // Quick check if controller already exists
          Get.find<LivestreamScreenController>(tag: roomID);
          _createdControllers.add(roomID);
          if (isActive) {
            Loggers.info('⚡ Fast activation - Found existing controller: $roomID');
          }
          return;
        } catch (e) {
          // Controller doesn't exist, create it immediately
          try {
            if (isActive) {
              Loggers.info('⚡ Fast creation for active stream: $roomID');
            } else {
              Loggers.info('🔄 Pre-loading controller for room: $roomID');
            }

            final newController = Get.put(
              LivestreamScreenController(livestream.obs, false),
              tag: roomID,
            );
            _createdControllers.add(roomID);
            _roomManager.markRoomAsJoined(roomID, roomID);

            if (isActive) {
              Loggers.success('⚡ Active controller created instantly: $roomID');
            } else {
              Loggers.success('✅ Pre-loaded controller created: $roomID');
            }
          } catch (createError) {
            Loggers.error('❌ Failed to create controller for room: $roomID, error: $createError');
            _createdControllers.remove(roomID);
            _roomManager.markRoomAsFailed(roomID, 'Controller creation failed: $createError');
          }
        }
      }
    }
  }

  void _activateStreamAtIndex(int index) {
    Loggers.info('🔄 _activateStreamAtIndex called with index: $index');

    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      Loggers.info('📋 Stream details - Index: $index, RoomID: $roomID, Host: ${livestream.hostUser?.username}');

      if (roomID != null) {
        Loggers.info('🚀 Activating stream for room: $roomID');
        _currentActiveRoomID = roomID;

        // Ensure controller exists for this stream
        _createControllerForIndex(index, isActive: true);

        // Pre-create controllers for adjacent streams
        _preCreateAdjacentControllers();
      } else {
        Loggers.error('❌ Cannot activate stream - roomID is null for index: $index');
      }
    } else {
      Loggers.error('❌ Invalid index: $index, livestreams length: ${widget.livestreams.length}');
    }
  }

  void _deactivateStreamAtIndex(int index) {
    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      if (roomID != null && _createdControllers.contains(roomID)) {
        Loggers.info('🛑 Deactivating stream at index $index for room: $roomID');
        try {
          // Find and properly dispose the controller
          final controller = Get.find<LivestreamScreenController>(tag: roomID);

          // Perform immediate cleanup for faster switching
          controller.logoutRoom(5).catchError((error) {
            Loggers.error('Error during logout for room $roomID: $error');
          });

          // Delete the controller immediately - don't wait for logout
          Get.delete<LivestreamScreenController>(tag: roomID);
          _createdControllers.remove(roomID);
          _roomManager.cleanupRoom(roomID);
          Loggers.success('✅ Controller disposed for room: $roomID');
        } catch (e) {
          Loggers.warning('⚠️ Controller not found for room: $roomID, error: $e');
          _createdControllers.remove(roomID);
          _roomManager.cleanupRoom(roomID);
        }
      }
    }
  }

  void _deactivateCurrentStream() {
    if (_currentActiveRoomID != null) {
      Loggers.info('🛑 Deactivating current stream for room: $_currentActiveRoomID');

      try {
        // Find and properly dispose the controller
        final controller = Get.find<LivestreamScreenController>(tag: _currentActiveRoomID);

        // Perform immediate cleanup for faster switching
        controller.logoutRoom(5).catchError((error) {
          Loggers.error('Error during logout for room $_currentActiveRoomID: $error');
        });

        // Delete the controller immediately - don't wait for logout
        Get.delete<LivestreamScreenController>(tag: _currentActiveRoomID);
        _createdControllers.remove(_currentActiveRoomID);
        Loggers.success('✅ Controller disposed for room: $_currentActiveRoomID');
      } catch (e) {
        Loggers.warning('⚠️ Controller not found for room: $_currentActiveRoomID, error: $e');
        _createdControllers.remove(_currentActiveRoomID);
      }

      _currentActiveRoomID = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, index) {
        final isActive = index == _currentIndex;
        final roomID = widget.livestreams[index].roomID;

        // OPTIMIZATION: Minimal logging for better performance
        if (isActive) {
          Loggers.info('⚡ Building ACTIVE stream - Index: $index, RoomID: $roomID');
        }

        // OPTIMIZATION: Controller should already exist from pre-loading
        // Only create if absolutely necessary
        if (isActive && roomID != null && !_createdControllers.contains(roomID)) {
          Loggers.warning('⚠️ Controller missing for active stream, creating instantly: $roomID');
          _createControllerForIndex(index, isActive: true);
        }

        return LiveStreamAudienceScreen(
          livestream: widget.livestreams[index],
          isHost: false,
          isActive: isActive,
        );
      },
    );
  }
}
